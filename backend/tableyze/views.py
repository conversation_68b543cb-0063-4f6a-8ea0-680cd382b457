from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsA<PERSON>enticated, AllowAny
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON>art<PERSON>ars<PERSON>, FormParser
from django.shortcuts import get_object_or_404
from django.contrib.auth.models import User
from django.conf import settings
import os
import uuid

from .models import Analysis, Dataset, Customer
from .serializers import (
    AnalysisSerializer,
    AnalysisDetailSerializer,
    DatasetSerializer,
    CustomerSerializer
)
from .helpers import compute_mic, get_top_mic_pairs
import pandas as pd
import numpy as np
import json


class AnalysisListCreateView(generics.ListCreateAPIView):
    """
    List all analyses for the authenticated user or create a new analysis
    """
    serializer_class = AnalysisSerializer
    permission_classes = [AllowAny]  # Temporarily allow any access for development

    def get_queryset(self):
        # Get or create a default user for development
        user, created = User.objects.get_or_create(
            username='demo_user',
            defaults={'email': '<EMAIL>', 'first_name': 'Demo', 'last_name': 'User'}
        )
        return Analysis.objects.filter(user=user, is_active=True)

    def perform_create(self, serializer):
        # Get or create a default user for development
        user, created = User.objects.get_or_create(
            username='demo_user',
            defaults={'email': '<EMAIL>', 'first_name': 'Demo', 'last_name': 'User'}
        )
        serializer.save(user=user)


class AnalysisDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete an analysis instance
    """
    serializer_class = AnalysisDetailSerializer
    permission_classes = [AllowAny]  # Temporarily allow any access for development

    def get_queryset(self):
        # Get or create a default user for development
        user, created = User.objects.get_or_create(
            username='demo_user',
            defaults={'email': '<EMAIL>', 'first_name': 'Demo', 'last_name': 'User'}
        )
        return Analysis.objects.filter(user=user)

    def perform_destroy(self, instance):
        # Soft delete - mark as inactive instead of deleting
        instance.is_active = False # TODO: Change to hard delete
        instance.save()


class DatasetListCreateView(generics.ListCreateAPIView):
    """
    List all datasets for an analysis or create a new dataset. On create, accept
    multipart file under key 'file', store locally, and return a basic preview
    of the first 5 rows using pandas (if installed).
    """
    serializer_class = DatasetSerializer
    permission_classes = [AllowAny]  # Temporarily allow any access for development
    parser_classes = [MultiPartParser, FormParser]

    def get_queryset(self):
        user, created = User.objects.get_or_create(
            username='demo_user',
            defaults={'email': '<EMAIL>', 'first_name': 'Demo', 'last_name': 'User'}
        )
        analysis_id = self.kwargs.get('analysis_id')
        return Dataset.objects.filter(
            analysis_id=analysis_id,
            user=user
        )

    def post(self, request, *args, **kwargs):
        # emulate create with custom handling
        user, created = User.objects.get_or_create(
            username='demo_user',
            defaults={'email': '<EMAIL>', 'first_name': 'Demo', 'last_name': 'User'}
        )
        analysis_id = self.kwargs.get('analysis_id')
        analysis = get_object_or_404(Analysis, id=analysis_id, user=user)

        uploaded_file = request.FILES.get('file')
        name = request.data.get('name') or (uploaded_file.name if uploaded_file else None)
        if not uploaded_file or not name:
            return Response({'detail': 'file and name are required'}, status=status.HTTP_400_BAD_REQUEST)

        # Validate size and extension
        max_bytes = 100 * 1024 * 1024
        if uploaded_file.size > max_bytes:
            return Response({'detail': 'File too large (>100MB).'}, status=status.HTTP_400_BAD_REQUEST)
        _, ext = os.path.splitext(uploaded_file.name.lower())
        if ext not in ['.csv', '.xls', '.xlsx']:
            return Response({'detail': 'Unsupported file type.'}, status=status.HTTP_400_BAD_REQUEST)

        # Save locally under MEDIA_ROOT
        media_root = getattr(settings, 'MEDIA_ROOT', None)
        media_url = getattr(settings, 'MEDIA_URL', '/media/')
        if not media_root:
            # fallback to project base images dir (as configured in settings for local)
            media_root = os.path.join(settings.BASE_DIR, 'images')
            os.makedirs(media_root, exist_ok=True)
        os.makedirs(media_root, exist_ok=True)

        uid = uuid.uuid4().hex
        stored_name = f"datasets/{uid}-{uploaded_file.name}"
        stored_path = os.path.join(media_root, stored_name)
        os.makedirs(os.path.dirname(stored_path), exist_ok=True)
        with open(stored_path, 'wb') as dest:
            for chunk in uploaded_file.chunks():
                dest.write(chunk)

        file_url = f"{media_url}{stored_name}" if media_url else stored_path

        # Create dataset
        dataset = Dataset.objects.create(
            user=user,
            analysis=analysis,
            name=name,
            file_url=file_url,
            status='raw'
        )

        # Basic parsing for preview
        preview = {'columns': [], 'rows': []}
        try:
            import pandas as pd  # optional: may not be installed yet
            abs_path = stored_path
            # pandas requires absolute path
            if not os.path.isabs(abs_path):
                abs_path = os.path.abspath(abs_path)
            if ext == '.csv':
                df = pd.read_csv(abs_path, nrows=5)
            else:
                df = pd.read_excel(abs_path, nrows=5)
            preview['columns'] = list(map(str, df.columns.tolist()))
            preview['rows'] = [list(map(lambda v: '' if pd.isna(v) else str(v), row)) for row in df.values.tolist()]
        except Exception as e:
            # If pandas not available or parsing fails, keep empty preview
            preview = {'columns': [], 'rows': []}

        serialized = DatasetSerializer(dataset).data
        return Response({'dataset': serialized, 'preview': preview}, status=status.HTTP_201_CREATED)

    def perform_create(self, serializer):
        # Not used due to custom post
        pass


class DatasetDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a dataset instance
    """
    serializer_class = DatasetSerializer
    permission_classes = [AllowAny]  # Temporarily allow any access for development

    def get_queryset(self):
        # Get or create a default user for development
        user, created = User.objects.get_or_create(
            username='demo_user',
            defaults={'email': '<EMAIL>', 'first_name': 'Demo', 'last_name': 'User'}
        )
        return Dataset.objects.filter(user=user)


@api_view(['GET'])
@permission_classes([AllowAny])
def dataset_preview(request, pk):
    """
    Return the first 5 rows preview for the given dataset.
    """
    # Get or create a default user for development
    user, created = User.objects.get_or_create(
        username='demo_user',
        defaults={'email': '<EMAIL>', 'first_name': 'Demo', 'last_name': 'User'}
    )
    dataset = get_object_or_404(Dataset, id=pk, user=user)

    # Derive absolute path from file_url
    file_url = dataset.file_url or ''
    media_url = getattr(settings, 'MEDIA_URL', '/media/')
    media_root = getattr(settings, 'MEDIA_ROOT', None) or os.path.join(settings.BASE_DIR, 'images')

    if file_url.startswith(media_url):
        rel_path = file_url[len(media_url):]
        abs_path = os.path.join(media_root, rel_path)
    else:
        # fallback: maybe absolute path already
        abs_path = file_url if os.path.isabs(file_url) else os.path.join(media_root, file_url)

    # Parse using pandas (first 5 rows only)
    preview = {'columns': [], 'rows': []}
    try:
        _, ext = os.path.splitext(abs_path.lower())
        if ext == '.csv':
            df = pd.read_csv(abs_path, nrows=5)
        else:
            df = pd.read_excel(abs_path, nrows=5)
        preview['columns'] = list(map(str, df.columns.tolist()))
        preview['rows'] = [list('' if pd.isna(v) else str(v) for v in row) for row in df.values.tolist()]
    except Exception as e:
        preview = {'columns': [], 'rows': []}

    return Response({'preview': preview})


@api_view(['GET', 'PATCH'])
@permission_classes([AllowAny])
def dataset_drop_columns(request, pk):
    """Persist and retrieve selected columns to drop for a dataset.
    For now, store in a sidecar JSON file next to the dataset path to avoid DB migration.
    """
    # Get or create a default user for development
    user, created = User.objects.get_or_create(
        username='demo_user',
        defaults={'email': '<EMAIL>', 'first_name': 'Demo', 'last_name': 'User'}
    )
    dataset = get_object_or_404(Dataset, id=pk, user=user)

    media_url = getattr(settings, 'MEDIA_URL', '/media/')
    media_root = getattr(settings, 'MEDIA_ROOT', None) or os.path.join(settings.BASE_DIR, 'images')
    file_url = dataset.file_url or ''
    if file_url.startswith(media_url):
        rel_path = file_url[len(media_url):]
        dataset_path = os.path.join(media_root, rel_path)
    else:
        dataset_path = file_url if os.path.isabs(file_url) else os.path.join(media_root, file_url)

    sidecar = dataset_path + '.drops.json'

    if request.method == 'PATCH':
        try:
            import json
            body = request.data.get('drop_columns')
            if isinstance(body, str):
                cols = json.loads(body)
            else:
                cols = body or []
            os.makedirs(os.path.dirname(sidecar), exist_ok=True)
            with open(sidecar, 'w') as f:
                json.dump({'drop_columns': cols}, f)
            return Response({'drop_columns': cols})
        except Exception:
            return Response({'detail': 'Failed to persist drop columns.'}, status=status.HTTP_400_BAD_REQUEST)

    # GET
    try:
        import json
        if os.path.exists(sidecar):
            with open(sidecar, 'r') as f:
                data = json.load(f)
            return Response({'drop_columns': data.get('drop_columns', [])})
    except Exception:
        pass
    return Response({'drop_columns': []})

@api_view(['POST'])
@permission_classes([AllowAny])
def dataset_analyze(request, pk):
    """
    Perform data analysis on the dataset:
    1. Drop user-selected columns and auto-detected categorical columns with unique values = row count
    2. Determine column types (numerical, categorical, date)
    3. Calculate statistics and histograms for each column
    4. Calculate correlations between numerical columns
    """
    # Get or create a default user for development
    user, created = User.objects.get_or_create(
        username='demo_user',
        defaults={'email': '<EMAIL>', 'first_name': 'Demo', 'last_name': 'User'}
    )
    dataset = get_object_or_404(Dataset, id=pk, user=user)

    # Get file path
    media_url = getattr(settings, 'MEDIA_URL', '/media/')
    media_root = getattr(settings, 'MEDIA_ROOT', None) or os.path.join(settings.BASE_DIR, 'images')
    file_url = dataset.file_url or ''
    if file_url.startswith(media_url):
        rel_path = file_url[len(media_url):]
        dataset_path = os.path.join(media_root, rel_path)
    else:
        dataset_path = file_url if os.path.isabs(file_url) else os.path.join(media_root, file_url)

    try:
        # Load full dataset
        _, ext = os.path.splitext(dataset_path.lower())
        if ext == '.csv':
            df = pd.read_csv(dataset_path)
        else:
            df = pd.read_excel(dataset_path)

        # Get user-selected columns to drop
        sidecar = dataset_path + '.drops.json'
        user_drops = []
        if os.path.exists(sidecar):
            with open(sidecar, 'r') as f:
                data = json.load(f)
                user_drops = data.get('drop_columns', [])

        # Auto-detect categorical columns with unique values = row count (excluding NaN)
        auto_drops = []
        for col in df.columns:
            if col in user_drops:
                continue
            non_null_count = df[col].count()  # excludes NaN
            unique_count = df[col].nunique()  # excludes NaN
            if non_null_count > 0 and unique_count == non_null_count:
                # Each non-null value is unique - likely an ID column
                auto_drops.append(col)

        # Drop columns
        all_drops = list(set(user_drops + auto_drops))
        df_clean = df.drop(columns=all_drops, errors='ignore')

        # Analyze remaining columns
        features = []
        numerical_cols = []
        categorical_cols = []

        for col in df_clean.columns:
            feature_info = {'name': col, 'type': 'unknown', 'stats': {}, 'histogram': {}}

            # Determine column type
            if pd.api.types.is_numeric_dtype(df_clean[col]):
                feature_info['type'] = 'numerical'
                numerical_cols.append(col)

                # Calculate numerical statistics
                col_data = df_clean[col].dropna()
                if len(col_data) > 0:
                    feature_info['stats'] = {
                        'mean': float(col_data.mean()),
                        'median': float(col_data.median()),
                        'min': float(col_data.min()),
                        'max': float(col_data.max()),
                        'std': float(col_data.std()) if len(col_data) > 1 else 0.0,
                        'count': int(len(col_data)),
                        'null_count': int(df_clean[col].isnull().sum())
                    }

                    # Create histogram
                    hist, bin_edges = np.histogram(col_data, bins=min(20, len(col_data.unique())))
                    feature_info['histogram'] = {
                        'counts': hist.tolist(),
                        'bin_edges': bin_edges.tolist()
                    }

            elif pd.api.types.is_datetime64_any_dtype(df_clean[col]):
                feature_info['type'] = 'date'
                # For dates, we could add date-specific stats later
                col_data = df_clean[col].dropna()
                feature_info['stats'] = {
                    'count': int(len(col_data)),
                    'null_count': int(df_clean[col].isnull().sum()),
                    'min_date': str(col_data.min()) if len(col_data) > 0 else None,
                    'max_date': str(col_data.max()) if len(col_data) > 0 else None
                }
            else:
                feature_info['type'] = 'categorical'
                categorical_cols.append(col)

                # Calculate categorical statistics
                col_data = df_clean[col].dropna()
                if len(col_data) > 0:
                    value_counts = col_data.value_counts()
                    feature_info['stats'] = {
                        'unique_count': int(col_data.nunique()),
                        'most_common': str(value_counts.index[0]) if len(value_counts) > 0 else None,
                        'most_common_count': int(value_counts.iloc[0]) if len(value_counts) > 0 else 0,
                        'count': int(len(col_data)),
                        'null_count': int(df_clean[col].isnull().sum())
                    }

                    # Create histogram for categorical (top 10 categories)
                    top_categories = value_counts.head(10)
                    feature_info['histogram'] = {
                        'categories': top_categories.index.tolist(),
                        'counts': top_categories.values.tolist()
                    }

            features.append(feature_info)

        # Calculate correlations for numerical columns
        correlations = []
        non_date_cols = numerical_cols + categorical_cols
        # TODO: Build a smart algorithm that takes the time complexity of MIC given the number of features and rows into account to determine the size of the subset.
        df_for_mic = df_clean[non_date_cols].iloc[:10000]
        mic_pairs = compute_mic(df_for_mic)
        correlations = get_top_mic_pairs(mic_pairs, top_n=5)

        # if len(numerical_cols) > 1:
        #     corr_matrix = df_clean[numerical_cols].corr()

        #     # Get top 5 correlations (excluding self-correlations)
        #     corr_pairs = []
        #     for i in range(len(numerical_cols)):
        #         for j in range(i+1, len(numerical_cols)):
        #             col1, col2 = numerical_cols[i], numerical_cols[j]
        #             corr_value = corr_matrix.loc[col1, col2]
        #             if not pd.isna(corr_value):
        #                 corr_pairs.append({
        #                     'feature1': col1,
        #                     'feature2': col2,
        #                     'correlation': float(abs(corr_value)),
        #                     'correlation_raw': float(corr_value)
        #                 })

        #     # Sort by absolute correlation and take top 5
        #     corr_pairs.sort(key=lambda x: x['correlation'], reverse=True)
        #     correlations = corr_pairs[:5]

        return Response({
            'features': features,
            'correlations': correlations,
            'dropped_columns': {
                'user_selected': user_drops,
                'auto_detected': auto_drops
            },
            'total_rows': len(df),
            'remaining_columns': len(df_clean.columns)
        })

    except Exception as e:
        return Response({
            'detail': f'Analysis failed: {str(e)}'
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([AllowAny])  # Temporarily allow any access for development
def dashboard_overview(request):
    """
    Get dashboard overview data for the demo user
    """
    # Get or create a default user for development
    user, created = User.objects.get_or_create(
        username='demo_user',
        defaults={'email': '<EMAIL>', 'first_name': 'Demo', 'last_name': 'User'}
    )

    # Get user's analyses
    analyses = Analysis.objects.filter(user=user, is_active=True)
    analyses_data = AnalysisSerializer(analyses, many=True).data

    return Response({
        'user': {
            'username': user.username,
            'email': user.email,
            'full_name': user.get_full_name()
        },
        'analyses': analyses_data,
    })
