{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Coding/tableyze/frontend/src/components/DataAnalysisContainer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';\nimport { Bar, Doughnut } from 'react-chartjs-2';\nimport ContainerBox from './ContainerBox';\nimport './DataAnalysisContainer.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement);\nconst DataAnalysisContainer = ({\n  analysis,\n  visible,\n  onAnalysisComplete\n}) => {\n  _s();\n  const [analysisData, setAnalysisData] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const isReady = analysisData && !loading;\n  useEffect(() => {\n    var _analysis$datasets, _analysis$datasets$;\n    if (visible && analysis !== null && analysis !== void 0 && (_analysis$datasets = analysis.datasets) !== null && _analysis$datasets !== void 0 && (_analysis$datasets$ = _analysis$datasets[0]) !== null && _analysis$datasets$ !== void 0 && _analysis$datasets$.id && !analysisData) {\n      performAnalysis();\n    }\n  }, [visible, analysis === null || analysis === void 0 ? void 0 : analysis.datasets, analysisData]);\n  const performAnalysis = async () => {\n    var _analysis$datasets2, _analysis$datasets2$;\n    if (!(analysis !== null && analysis !== void 0 && (_analysis$datasets2 = analysis.datasets) !== null && _analysis$datasets2 !== void 0 && (_analysis$datasets2$ = _analysis$datasets2[0]) !== null && _analysis$datasets2$ !== void 0 && _analysis$datasets2$.id)) return;\n    setLoading(true);\n    setError(null);\n    try {\n      const {\n        apiService\n      } = await import('../services/api');\n      const response = await apiService.analyzeDataset(analysis.datasets[0].id);\n      setAnalysisData(response.data);\n      onAnalysisComplete === null || onAnalysisComplete === void 0 ? void 0 : onAnalysisComplete(response.data);\n    } catch (err) {\n      setError('Failed to analyze dataset. Please try again.');\n      console.error('Analysis error:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const createHistogramData = feature => {\n    if (feature.type === 'numerical') {\n      const {\n        counts,\n        bin_edges\n      } = feature.histogram;\n      const labels = bin_edges.slice(0, -1).map((edge, i) => `${edge.toFixed(2)}-${bin_edges[i + 1].toFixed(2)}`);\n      return {\n        labels,\n        datasets: [{\n          data: counts,\n          backgroundColor: 'rgba(59, 130, 246, 0.6)',\n          borderColor: 'rgba(59, 130, 246, 1)',\n          borderWidth: 1\n        }]\n      };\n    } else if (feature.type === 'categorical') {\n      const {\n        categories,\n        counts\n      } = feature.histogram;\n      return {\n        labels: categories,\n        datasets: [{\n          data: counts,\n          backgroundColor: 'rgba(139, 92, 246, 0.6)',\n          borderColor: 'rgba(139, 92, 246, 1)',\n          borderWidth: 1\n        }]\n      };\n    }\n    return null;\n  };\n  const createCorrelationGaugeData = correlation => {\n    const absCorr = Math.abs(correlation.correlation_raw);\n    const remaining = 1 - absCorr;\n\n    // Color based on correlation strength: red (low) to green (high)\n    // Using more muted colors to avoid overly bright green\n    let color;\n    if (absCorr < 0.3) {\n      // Low correlation: red tones\n      const intensity = absCorr / 0.3;\n      color = `rgb(${Math.round(239 + (220 - 239) * intensity)}, ${Math.round(68 + (38 - 68) * intensity)}, ${Math.round(68 + (38 - 68) * intensity)})`;\n    } else if (absCorr < 0.7) {\n      // Medium correlation: orange to yellow tones\n      const intensity = (absCorr - 0.3) / 0.4;\n      color = `rgb(${Math.round(220 + (245 - 220) * intensity)}, ${Math.round(38 + (158 - 38) * intensity)}, ${Math.round(38 + (11 - 38) * intensity)})`;\n    } else {\n      // High correlation: muted green tones (avoiding bright green)\n      const intensity = (absCorr - 0.7) / 0.3;\n      color = `rgb(${Math.round(245 + (34 - 245) * intensity)}, ${Math.round(158 + (197 - 158) * intensity)}, ${Math.round(11 + (94 - 11) * intensity)})`;\n    }\n    return {\n      labels: ['Correlation', 'Remaining'],\n      datasets: [{\n        data: [absCorr, remaining],\n        backgroundColor: [color, 'rgba(229, 231, 235, 0.3)'],\n        borderWidth: 0,\n        cutout: '70%'\n      }]\n    };\n  };\n  const chartOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        display: false\n      },\n      tooltip: {\n        enabled: true\n      }\n    },\n    scales: {\n      x: {\n        display: false\n      },\n      y: {\n        display: false\n      }\n    }\n  };\n  const gaugeOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        display: false\n      },\n      tooltip: {\n        enabled: false\n      }\n    }\n  };\n  if (!visible) return null;\n  return /*#__PURE__*/_jsxDEV(ContainerBox, {\n    title: \"2. Data Analysis\",\n    isReady: isReady,\n    readyLabel: \"Target Selection\",\n    onReadyClick: () => {/* parent can handle next container */},\n    children: [loading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"analysis-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Analyzing your data...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 9\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"analysis-error\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary btn-sm\",\n        onClick: performAnalysis,\n        children: \"Retry Analysis\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 9\n    }, this), analysisData && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"analysis-results\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"analysis-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"analysis-section__title\",\n          children: \"Features\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"features-scroll\",\n          children: analysisData.features.map((feature, index) => {\n            var _feature$stats$mean, _feature$stats$median, _feature$stats$min, _feature$stats$max;\n            const histogramData = createHistogramData(feature);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-card__header\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `feature-type feature-type--${feature.type}`,\n                  children: feature.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"feature-card__name\",\n                  children: feature.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-card__chart\",\n                children: histogramData && /*#__PURE__*/_jsxDEV(Bar, {\n                  data: histogramData,\n                  options: chartOptions\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-card__stats\",\n                children: [feature.type === 'numerical' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"stat-label\",\n                      children: \"Mean:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 195,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"stat-value\",\n                      children: (_feature$stats$mean = feature.stats.mean) === null || _feature$stats$mean === void 0 ? void 0 : _feature$stats$mean.toFixed(2)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 196,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 194,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"stat-label\",\n                      children: \"Median:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 199,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"stat-value\",\n                      children: (_feature$stats$median = feature.stats.median) === null || _feature$stats$median === void 0 ? void 0 : _feature$stats$median.toFixed(2)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 200,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 198,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"stat-label\",\n                      children: \"Min:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 203,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"stat-value\",\n                      children: (_feature$stats$min = feature.stats.min) === null || _feature$stats$min === void 0 ? void 0 : _feature$stats$min.toFixed(2)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 204,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 202,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"stat-label\",\n                      children: \"Max:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 207,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"stat-value\",\n                      children: (_feature$stats$max = feature.stats.max) === null || _feature$stats$max === void 0 ? void 0 : _feature$stats$max.toFixed(2)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 208,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 206,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true), feature.type === 'categorical' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"stat-label\",\n                      children: \"Most Common:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 216,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"stat-value\",\n                      children: feature.stats.most_common\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 217,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"stat-label\",\n                      children: \"Unique Values:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 220,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"stat-value\",\n                      children: feature.stats.unique_count\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 221,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"stat-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"stat-label\",\n                    children: \"Count:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 227,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"stat-value\",\n                    children: feature.stats.count\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 228,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 23\n                }, this), feature.stats.null_count > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"stat-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"stat-label\",\n                    children: \"Missing:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"stat-value\",\n                    children: feature.stats.null_count\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 11\n      }, this), analysisData.correlations.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"analysis-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"analysis-section__title\",\n          children: \"Correlations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"correlations-grid\",\n          children: analysisData.correlations.map((corr, index) => {\n            const gaugeData = createCorrelationGaugeData(corr);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"correlation-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"correlation-card__chart\",\n                children: [/*#__PURE__*/_jsxDEV(Doughnut, {\n                  data: gaugeData,\n                  options: gaugeOptions\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"correlation-card__value\",\n                  children: Math.abs(corr.correlation_raw).toFixed(2)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"correlation-card__labels\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"correlation-card__feature\",\n                  children: corr.feature1\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"correlation-card__vs\",\n                  children: \"vs\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"correlation-card__feature\",\n                  children: corr.feature2\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 23\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 21\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"analysis-summary\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Analyzed \", analysisData.total_rows, \" rows with \", analysisData.remaining_columns, \" features\", analysisData.dropped_columns.user_selected.length + analysisData.dropped_columns.auto_detected.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\" (dropped \", analysisData.dropped_columns.user_selected.length + analysisData.dropped_columns.auto_detected.length, \" columns)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 146,\n    columnNumber: 5\n  }, this);\n};\n_s(DataAnalysisContainer, \"e8ICjzRBVDSssGCCUkN+bIWqBnc=\");\n_c = DataAnalysisContainer;\nexport default DataAnalysisContainer;\nvar _c;\n$RefreshReg$(_c, \"DataAnalysisContainer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Chart", "ChartJS", "CategoryScale", "LinearScale", "BarElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "ArcElement", "Bar", "Doughnut", "ContainerBox", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "register", "DataAnalysisContainer", "analysis", "visible", "onAnalysisComplete", "_s", "analysisData", "setAnalysisData", "loading", "setLoading", "error", "setError", "isReady", "_analysis$datasets", "_analysis$datasets$", "datasets", "id", "performAnalysis", "_analysis$datasets2", "_analysis$datasets2$", "apiService", "response", "analyzeDataset", "data", "err", "console", "createHistogramData", "feature", "type", "counts", "bin_edges", "histogram", "labels", "slice", "map", "edge", "i", "toFixed", "backgroundColor", "borderColor", "borderWidth", "categories", "createCorrelationGaugeData", "correlation", "absCorr", "Math", "abs", "correlation_raw", "remaining", "color", "intensity", "round", "cutout", "chartOptions", "responsive", "maintainAspectRatio", "plugins", "legend", "display", "tooltip", "enabled", "scales", "x", "y", "gaugeOptions", "title", "readyLabel", "onReadyClick", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "features", "index", "_feature$stats$mean", "_feature$stats$median", "_feature$stats$min", "_feature$stats$max", "histogramData", "name", "options", "stats", "mean", "median", "min", "max", "most_common", "unique_count", "count", "null_count", "correlations", "length", "corr", "gaugeData", "feature1", "feature2", "total_rows", "remaining_columns", "dropped_columns", "user_selected", "auto_detected", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Coding/tableyze/frontend/src/components/DataAnalysisContainer.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n  ArcElement,\n} from 'chart.js';\nimport { Bar, Doughnut } from 'react-chartjs-2';\nimport ContainerBox from './ContainerBox';\nimport './DataAnalysisContainer.css';\n\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n  ArcElement\n);\n\nconst DataAnalysisContainer = ({ analysis, visible, onAnalysisComplete }) => {\n  const [analysisData, setAnalysisData] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  const isReady = analysisData && !loading;\n\n  useEffect(() => {\n    if (visible && analysis?.datasets?.[0]?.id && !analysisData) {\n      performAnalysis();\n    }\n  }, [visible, analysis?.datasets, analysisData]);\n\n  const performAnalysis = async () => {\n    if (!analysis?.datasets?.[0]?.id) return;\n    \n    setLoading(true);\n    setError(null);\n    \n    try {\n      const { apiService } = await import('../services/api');\n      const response = await apiService.analyzeDataset(analysis.datasets[0].id);\n      setAnalysisData(response.data);\n      onAnalysisComplete?.(response.data);\n    } catch (err) {\n      setError('Failed to analyze dataset. Please try again.');\n      console.error('Analysis error:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const createHistogramData = (feature) => {\n    if (feature.type === 'numerical') {\n      const { counts, bin_edges } = feature.histogram;\n      const labels = bin_edges.slice(0, -1).map((edge, i) => \n        `${edge.toFixed(2)}-${bin_edges[i + 1].toFixed(2)}`\n      );\n      \n      return {\n        labels,\n        datasets: [{\n          data: counts,\n          backgroundColor: 'rgba(59, 130, 246, 0.6)',\n          borderColor: 'rgba(59, 130, 246, 1)',\n          borderWidth: 1,\n        }]\n      };\n    } else if (feature.type === 'categorical') {\n      const { categories, counts } = feature.histogram;\n      return {\n        labels: categories,\n        datasets: [{\n          data: counts,\n          backgroundColor: 'rgba(139, 92, 246, 0.6)',\n          borderColor: 'rgba(139, 92, 246, 1)',\n          borderWidth: 1,\n        }]\n      };\n    }\n    return null;\n  };\n\n  const createCorrelationGaugeData = (correlation) => {\n    const absCorr = Math.abs(correlation.correlation_raw);\n    const remaining = 1 - absCorr;\n\n    // Color based on correlation strength: red (low) to green (high)\n    // Using more muted colors to avoid overly bright green\n    let color;\n    if (absCorr < 0.3) {\n      // Low correlation: red tones\n      const intensity = absCorr / 0.3;\n      color = `rgb(${Math.round(239 + (220 - 239) * intensity)}, ${Math.round(68 + (38 - 68) * intensity)}, ${Math.round(68 + (38 - 68) * intensity)})`;\n    } else if (absCorr < 0.7) {\n      // Medium correlation: orange to yellow tones\n      const intensity = (absCorr - 0.3) / 0.4;\n      color = `rgb(${Math.round(220 + (245 - 220) * intensity)}, ${Math.round(38 + (158 - 38) * intensity)}, ${Math.round(38 + (11 - 38) * intensity)})`;\n    } else {\n      // High correlation: muted green tones (avoiding bright green)\n      const intensity = (absCorr - 0.7) / 0.3;\n      color = `rgb(${Math.round(245 + (34 - 245) * intensity)}, ${Math.round(158 + (197 - 158) * intensity)}, ${Math.round(11 + (94 - 11) * intensity)})`;\n    }\n\n    return {\n      labels: ['Correlation', 'Remaining'],\n      datasets: [{\n        data: [absCorr, remaining],\n        backgroundColor: [color, 'rgba(229, 231, 235, 0.3)'],\n        borderWidth: 0,\n        cutout: '70%',\n      }]\n    };\n  };\n\n  const chartOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: { display: false },\n      tooltip: { enabled: true }\n    },\n    scales: {\n      x: { display: false },\n      y: { display: false }\n    }\n  };\n\n  const gaugeOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: { display: false },\n      tooltip: { enabled: false }\n    }\n  };\n\n  if (!visible) return null;\n\n  return (\n    <ContainerBox\n      title=\"2. Data Analysis\"\n      isReady={isReady}\n      readyLabel=\"Target Selection\"\n      onReadyClick={() => {/* parent can handle next container */}}\n    >\n      {loading && (\n        <div className=\"analysis-loading\">\n          <div className=\"loading-spinner\"></div>\n          <p>Analyzing your data...</p>\n        </div>\n      )}\n\n      {error && (\n        <div className=\"analysis-error\">\n          <p>{error}</p>\n          <button className=\"btn btn-primary btn-sm\" onClick={performAnalysis}>\n            Retry Analysis\n          </button>\n        </div>\n      )}\n\n      {analysisData && (\n        <div className=\"analysis-results\">\n          {/* Features Section */}\n          <div className=\"analysis-section\">\n            <h3 className=\"analysis-section__title\">Features</h3>\n            <div className=\"features-scroll\">\n              {analysisData.features.map((feature, index) => {\n                const histogramData = createHistogramData(feature);\n                return (\n                  <div key={index} className=\"feature-card\">\n                    <div className=\"feature-card__header\">\n                      <span className={`feature-type feature-type--${feature.type}`}>\n                        {feature.type}\n                      </span>\n                      <h4 className=\"feature-card__name\">{feature.name}</h4>\n                    </div>\n                    \n                    <div className=\"feature-card__chart\">\n                      {histogramData && (\n                        <Bar data={histogramData} options={chartOptions} />\n                      )}\n                    </div>\n                    \n                    <div className=\"feature-card__stats\">\n                      {feature.type === 'numerical' && (\n                        <>\n                          <div className=\"stat-item\">\n                            <span className=\"stat-label\">Mean:</span>\n                            <span className=\"stat-value\">{feature.stats.mean?.toFixed(2)}</span>\n                          </div>\n                          <div className=\"stat-item\">\n                            <span className=\"stat-label\">Median:</span>\n                            <span className=\"stat-value\">{feature.stats.median?.toFixed(2)}</span>\n                          </div>\n                          <div className=\"stat-item\">\n                            <span className=\"stat-label\">Min:</span>\n                            <span className=\"stat-value\">{feature.stats.min?.toFixed(2)}</span>\n                          </div>\n                          <div className=\"stat-item\">\n                            <span className=\"stat-label\">Max:</span>\n                            <span className=\"stat-value\">{feature.stats.max?.toFixed(2)}</span>\n                          </div>\n                        </>\n                      )}\n                      \n                      {feature.type === 'categorical' && (\n                        <>\n                          <div className=\"stat-item\">\n                            <span className=\"stat-label\">Most Common:</span>\n                            <span className=\"stat-value\">{feature.stats.most_common}</span>\n                          </div>\n                          <div className=\"stat-item\">\n                            <span className=\"stat-label\">Unique Values:</span>\n                            <span className=\"stat-value\">{feature.stats.unique_count}</span>\n                          </div>\n                        </>\n                      )}\n                      \n                      <div className=\"stat-item\">\n                        <span className=\"stat-label\">Count:</span>\n                        <span className=\"stat-value\">{feature.stats.count}</span>\n                      </div>\n                      \n                      {feature.stats.null_count > 0 && (\n                        <div className=\"stat-item\">\n                          <span className=\"stat-label\">Missing:</span>\n                          <span className=\"stat-value\">{feature.stats.null_count}</span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Correlations Section */}\n          {analysisData.correlations.length > 0 && (\n            <div className=\"analysis-section\">\n              <h3 className=\"analysis-section__title\">Correlations</h3>\n              <div className=\"correlations-grid\">\n                {analysisData.correlations.map((corr, index) => {\n                  const gaugeData = createCorrelationGaugeData(corr);\n                  return (\n                    <div key={index} className=\"correlation-card\">\n                      <div className=\"correlation-card__chart\">\n                        <Doughnut data={gaugeData} options={gaugeOptions} />\n                        <div className=\"correlation-card__value\">\n                          {Math.abs(corr.correlation_raw).toFixed(2)}\n                        </div>\n                      </div>\n                      <div className=\"correlation-card__labels\">\n                        <div className=\"correlation-card__feature\">{corr.feature1}</div>\n                        <div className=\"correlation-card__vs\">vs</div>\n                        <div className=\"correlation-card__feature\">{corr.feature2}</div>\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            </div>\n          )}\n\n          {/* Summary */}\n          <div className=\"analysis-summary\">\n            <p>\n              Analyzed {analysisData.total_rows} rows with {analysisData.remaining_columns} features\n              {analysisData.dropped_columns.user_selected.length + analysisData.dropped_columns.auto_detected.length > 0 && (\n                <span> (dropped {analysisData.dropped_columns.user_selected.length + analysisData.dropped_columns.auto_detected.length} columns)</span>\n              )}\n            </p>\n          </div>\n        </div>\n      )}\n    </ContainerBox>\n  );\n};\n\nexport default DataAnalysisContainer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,KAAK,IAAIC,OAAO,EAChBC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UAAU,QACL,UAAU;AACjB,SAASC,GAAG,EAAEC,QAAQ,QAAQ,iBAAiB;AAC/C,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAO,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErCd,OAAO,CAACe,QAAQ,CACdd,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UACF,CAAC;AAED,MAAMS,qBAAqB,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,OAAO;EAAEC;AAAmB,CAAC,KAAK;EAAAC,EAAA;EAC3E,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAM8B,OAAO,GAAGN,YAAY,IAAI,CAACE,OAAO;EAExCzB,SAAS,CAAC,MAAM;IAAA,IAAA8B,kBAAA,EAAAC,mBAAA;IACd,IAAIX,OAAO,IAAID,QAAQ,aAARA,QAAQ,gBAAAW,kBAAA,GAARX,QAAQ,CAAEa,QAAQ,cAAAF,kBAAA,gBAAAC,mBAAA,GAAlBD,kBAAA,CAAqB,CAAC,CAAC,cAAAC,mBAAA,eAAvBA,mBAAA,CAAyBE,EAAE,IAAI,CAACV,YAAY,EAAE;MAC3DW,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACd,OAAO,EAAED,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEa,QAAQ,EAAET,YAAY,CAAC,CAAC;EAE/C,MAAMW,eAAe,GAAG,MAAAA,CAAA,KAAY;IAAA,IAAAC,mBAAA,EAAAC,oBAAA;IAClC,IAAI,EAACjB,QAAQ,aAARA,QAAQ,gBAAAgB,mBAAA,GAARhB,QAAQ,CAAEa,QAAQ,cAAAG,mBAAA,gBAAAC,oBAAA,GAAlBD,mBAAA,CAAqB,CAAC,CAAC,cAAAC,oBAAA,eAAvBA,oBAAA,CAAyBH,EAAE,GAAE;IAElCP,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAM;QAAES;MAAW,CAAC,GAAG,MAAM,MAAM,CAAC,iBAAiB,CAAC;MACtD,MAAMC,QAAQ,GAAG,MAAMD,UAAU,CAACE,cAAc,CAACpB,QAAQ,CAACa,QAAQ,CAAC,CAAC,CAAC,CAACC,EAAE,CAAC;MACzET,eAAe,CAACc,QAAQ,CAACE,IAAI,CAAC;MAC9BnB,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAGiB,QAAQ,CAACE,IAAI,CAAC;IACrC,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZb,QAAQ,CAAC,8CAA8C,CAAC;MACxDc,OAAO,CAACf,KAAK,CAAC,iBAAiB,EAAEc,GAAG,CAAC;IACvC,CAAC,SAAS;MACRf,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiB,mBAAmB,GAAIC,OAAO,IAAK;IACvC,IAAIA,OAAO,CAACC,IAAI,KAAK,WAAW,EAAE;MAChC,MAAM;QAAEC,MAAM;QAAEC;MAAU,CAAC,GAAGH,OAAO,CAACI,SAAS;MAC/C,MAAMC,MAAM,GAAGF,SAAS,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,CAAC,KAChD,GAAGD,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,IAAIP,SAAS,CAACM,CAAC,GAAG,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,EACnD,CAAC;MAED,OAAO;QACLL,MAAM;QACNjB,QAAQ,EAAE,CAAC;UACTQ,IAAI,EAAEM,MAAM;UACZS,eAAe,EAAE,yBAAyB;UAC1CC,WAAW,EAAE,uBAAuB;UACpCC,WAAW,EAAE;QACf,CAAC;MACH,CAAC;IACH,CAAC,MAAM,IAAIb,OAAO,CAACC,IAAI,KAAK,aAAa,EAAE;MACzC,MAAM;QAAEa,UAAU;QAAEZ;MAAO,CAAC,GAAGF,OAAO,CAACI,SAAS;MAChD,OAAO;QACLC,MAAM,EAAES,UAAU;QAClB1B,QAAQ,EAAE,CAAC;UACTQ,IAAI,EAAEM,MAAM;UACZS,eAAe,EAAE,yBAAyB;UAC1CC,WAAW,EAAE,uBAAuB;UACpCC,WAAW,EAAE;QACf,CAAC;MACH,CAAC;IACH;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAME,0BAA0B,GAAIC,WAAW,IAAK;IAClD,MAAMC,OAAO,GAAGC,IAAI,CAACC,GAAG,CAACH,WAAW,CAACI,eAAe,CAAC;IACrD,MAAMC,SAAS,GAAG,CAAC,GAAGJ,OAAO;;IAE7B;IACA;IACA,IAAIK,KAAK;IACT,IAAIL,OAAO,GAAG,GAAG,EAAE;MACjB;MACA,MAAMM,SAAS,GAAGN,OAAO,GAAG,GAAG;MAC/BK,KAAK,GAAG,OAAOJ,IAAI,CAACM,KAAK,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,IAAID,SAAS,CAAC,KAAKL,IAAI,CAACM,KAAK,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,IAAID,SAAS,CAAC,KAAKL,IAAI,CAACM,KAAK,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,IAAID,SAAS,CAAC,GAAG;IACnJ,CAAC,MAAM,IAAIN,OAAO,GAAG,GAAG,EAAE;MACxB;MACA,MAAMM,SAAS,GAAG,CAACN,OAAO,GAAG,GAAG,IAAI,GAAG;MACvCK,KAAK,GAAG,OAAOJ,IAAI,CAACM,KAAK,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,IAAID,SAAS,CAAC,KAAKL,IAAI,CAACM,KAAK,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,EAAE,IAAID,SAAS,CAAC,KAAKL,IAAI,CAACM,KAAK,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,IAAID,SAAS,CAAC,GAAG;IACpJ,CAAC,MAAM;MACL;MACA,MAAMA,SAAS,GAAG,CAACN,OAAO,GAAG,GAAG,IAAI,GAAG;MACvCK,KAAK,GAAG,OAAOJ,IAAI,CAACM,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAID,SAAS,CAAC,KAAKL,IAAI,CAACM,KAAK,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,IAAID,SAAS,CAAC,KAAKL,IAAI,CAACM,KAAK,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,IAAID,SAAS,CAAC,GAAG;IACrJ;IAEA,OAAO;MACLlB,MAAM,EAAE,CAAC,aAAa,EAAE,WAAW,CAAC;MACpCjB,QAAQ,EAAE,CAAC;QACTQ,IAAI,EAAE,CAACqB,OAAO,EAAEI,SAAS,CAAC;QAC1BV,eAAe,EAAE,CAACW,KAAK,EAAE,0BAA0B,CAAC;QACpDT,WAAW,EAAE,CAAC;QACdY,MAAM,EAAE;MACV,CAAC;IACH,CAAC;EACH,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBC,UAAU,EAAE,IAAI;IAChBC,mBAAmB,EAAE,KAAK;IAC1BC,OAAO,EAAE;MACPC,MAAM,EAAE;QAAEC,OAAO,EAAE;MAAM,CAAC;MAC1BC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAK;IAC3B,CAAC;IACDC,MAAM,EAAE;MACNC,CAAC,EAAE;QAAEJ,OAAO,EAAE;MAAM,CAAC;MACrBK,CAAC,EAAE;QAAEL,OAAO,EAAE;MAAM;IACtB;EACF,CAAC;EAED,MAAMM,YAAY,GAAG;IACnBV,UAAU,EAAE,IAAI;IAChBC,mBAAmB,EAAE,KAAK;IAC1BC,OAAO,EAAE;MACPC,MAAM,EAAE;QAAEC,OAAO,EAAE;MAAM,CAAC;MAC1BC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAM;IAC5B;EACF,CAAC;EAED,IAAI,CAACzD,OAAO,EAAE,OAAO,IAAI;EAEzB,oBACEN,OAAA,CAACF,YAAY;IACXsE,KAAK,EAAC,kBAAkB;IACxBrD,OAAO,EAAEA,OAAQ;IACjBsD,UAAU,EAAC,kBAAkB;IAC7BC,YAAY,EAAEA,CAAA,KAAM,CAAC,uCAAwC;IAAAC,QAAA,GAE5D5D,OAAO,iBACNX,OAAA;MAAKwE,SAAS,EAAC,kBAAkB;MAAAD,QAAA,gBAC/BvE,OAAA;QAAKwE,SAAS,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvC5E,OAAA;QAAAuE,QAAA,EAAG;MAAsB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACN,EAEA/D,KAAK,iBACJb,OAAA;MAAKwE,SAAS,EAAC,gBAAgB;MAAAD,QAAA,gBAC7BvE,OAAA;QAAAuE,QAAA,EAAI1D;MAAK;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACd5E,OAAA;QAAQwE,SAAS,EAAC,wBAAwB;QAACK,OAAO,EAAEzD,eAAgB;QAAAmD,QAAA,EAAC;MAErE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,EAEAnE,YAAY,iBACXT,OAAA;MAAKwE,SAAS,EAAC,kBAAkB;MAAAD,QAAA,gBAE/BvE,OAAA;QAAKwE,SAAS,EAAC,kBAAkB;QAAAD,QAAA,gBAC/BvE,OAAA;UAAIwE,SAAS,EAAC,yBAAyB;UAAAD,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrD5E,OAAA;UAAKwE,SAAS,EAAC,iBAAiB;UAAAD,QAAA,EAC7B9D,YAAY,CAACqE,QAAQ,CAACzC,GAAG,CAAC,CAACP,OAAO,EAAEiD,KAAK,KAAK;YAAA,IAAAC,mBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,kBAAA;YAC7C,MAAMC,aAAa,GAAGvD,mBAAmB,CAACC,OAAO,CAAC;YAClD,oBACE9B,OAAA;cAAiBwE,SAAS,EAAC,cAAc;cAAAD,QAAA,gBACvCvE,OAAA;gBAAKwE,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,gBACnCvE,OAAA;kBAAMwE,SAAS,EAAE,8BAA8B1C,OAAO,CAACC,IAAI,EAAG;kBAAAwC,QAAA,EAC3DzC,OAAO,CAACC;gBAAI;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACP5E,OAAA;kBAAIwE,SAAS,EAAC,oBAAoB;kBAAAD,QAAA,EAAEzC,OAAO,CAACuD;gBAAI;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,eAEN5E,OAAA;gBAAKwE,SAAS,EAAC,qBAAqB;gBAAAD,QAAA,EACjCa,aAAa,iBACZpF,OAAA,CAACJ,GAAG;kBAAC8B,IAAI,EAAE0D,aAAc;kBAACE,OAAO,EAAE9B;gBAAa;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cACnD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN5E,OAAA;gBAAKwE,SAAS,EAAC,qBAAqB;gBAAAD,QAAA,GACjCzC,OAAO,CAACC,IAAI,KAAK,WAAW,iBAC3B/B,OAAA,CAAAE,SAAA;kBAAAqE,QAAA,gBACEvE,OAAA;oBAAKwE,SAAS,EAAC,WAAW;oBAAAD,QAAA,gBACxBvE,OAAA;sBAAMwE,SAAS,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAAK;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACzC5E,OAAA;sBAAMwE,SAAS,EAAC,YAAY;sBAAAD,QAAA,GAAAS,mBAAA,GAAElD,OAAO,CAACyD,KAAK,CAACC,IAAI,cAAAR,mBAAA,uBAAlBA,mBAAA,CAAoBxC,OAAO,CAAC,CAAC;oBAAC;sBAAAiC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC,eACN5E,OAAA;oBAAKwE,SAAS,EAAC,WAAW;oBAAAD,QAAA,gBACxBvE,OAAA;sBAAMwE,SAAS,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAAO;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC3C5E,OAAA;sBAAMwE,SAAS,EAAC,YAAY;sBAAAD,QAAA,GAAAU,qBAAA,GAAEnD,OAAO,CAACyD,KAAK,CAACE,MAAM,cAAAR,qBAAA,uBAApBA,qBAAA,CAAsBzC,OAAO,CAAC,CAAC;oBAAC;sBAAAiC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CAAC,eACN5E,OAAA;oBAAKwE,SAAS,EAAC,WAAW;oBAAAD,QAAA,gBACxBvE,OAAA;sBAAMwE,SAAS,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAAI;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACxC5E,OAAA;sBAAMwE,SAAS,EAAC,YAAY;sBAAAD,QAAA,GAAAW,kBAAA,GAAEpD,OAAO,CAACyD,KAAK,CAACG,GAAG,cAAAR,kBAAA,uBAAjBA,kBAAA,CAAmB1C,OAAO,CAAC,CAAC;oBAAC;sBAAAiC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE,CAAC,eACN5E,OAAA;oBAAKwE,SAAS,EAAC,WAAW;oBAAAD,QAAA,gBACxBvE,OAAA;sBAAMwE,SAAS,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAAI;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACxC5E,OAAA;sBAAMwE,SAAS,EAAC,YAAY;sBAAAD,QAAA,GAAAY,kBAAA,GAAErD,OAAO,CAACyD,KAAK,CAACI,GAAG,cAAAR,kBAAA,uBAAjBA,kBAAA,CAAmB3C,OAAO,CAAC,CAAC;oBAAC;sBAAAiC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE,CAAC;gBAAA,eACN,CACH,EAEA9C,OAAO,CAACC,IAAI,KAAK,aAAa,iBAC7B/B,OAAA,CAAAE,SAAA;kBAAAqE,QAAA,gBACEvE,OAAA;oBAAKwE,SAAS,EAAC,WAAW;oBAAAD,QAAA,gBACxBvE,OAAA;sBAAMwE,SAAS,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAAY;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAChD5E,OAAA;sBAAMwE,SAAS,EAAC,YAAY;sBAAAD,QAAA,EAAEzC,OAAO,CAACyD,KAAK,CAACK;oBAAW;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D,CAAC,eACN5E,OAAA;oBAAKwE,SAAS,EAAC,WAAW;oBAAAD,QAAA,gBACxBvE,OAAA;sBAAMwE,SAAS,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAClD5E,OAAA;sBAAMwE,SAAS,EAAC,YAAY;sBAAAD,QAAA,EAAEzC,OAAO,CAACyD,KAAK,CAACM;oBAAY;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC;gBAAA,eACN,CACH,eAED5E,OAAA;kBAAKwE,SAAS,EAAC,WAAW;kBAAAD,QAAA,gBACxBvE,OAAA;oBAAMwE,SAAS,EAAC,YAAY;oBAAAD,QAAA,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC1C5E,OAAA;oBAAMwE,SAAS,EAAC,YAAY;oBAAAD,QAAA,EAAEzC,OAAO,CAACyD,KAAK,CAACO;kBAAK;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,EAEL9C,OAAO,CAACyD,KAAK,CAACQ,UAAU,GAAG,CAAC,iBAC3B/F,OAAA;kBAAKwE,SAAS,EAAC,WAAW;kBAAAD,QAAA,gBACxBvE,OAAA;oBAAMwE,SAAS,EAAC,YAAY;oBAAAD,QAAA,EAAC;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5C5E,OAAA;oBAAMwE,SAAS,EAAC,YAAY;oBAAAD,QAAA,EAAEzC,OAAO,CAACyD,KAAK,CAACQ;kBAAU;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA,GA5DEG,KAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA6DV,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLnE,YAAY,CAACuF,YAAY,CAACC,MAAM,GAAG,CAAC,iBACnCjG,OAAA;QAAKwE,SAAS,EAAC,kBAAkB;QAAAD,QAAA,gBAC/BvE,OAAA;UAAIwE,SAAS,EAAC,yBAAyB;UAAAD,QAAA,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzD5E,OAAA;UAAKwE,SAAS,EAAC,mBAAmB;UAAAD,QAAA,EAC/B9D,YAAY,CAACuF,YAAY,CAAC3D,GAAG,CAAC,CAAC6D,IAAI,EAAEnB,KAAK,KAAK;YAC9C,MAAMoB,SAAS,GAAGtD,0BAA0B,CAACqD,IAAI,CAAC;YAClD,oBACElG,OAAA;cAAiBwE,SAAS,EAAC,kBAAkB;cAAAD,QAAA,gBAC3CvE,OAAA;gBAAKwE,SAAS,EAAC,yBAAyB;gBAAAD,QAAA,gBACtCvE,OAAA,CAACH,QAAQ;kBAAC6B,IAAI,EAAEyE,SAAU;kBAACb,OAAO,EAAEnB;gBAAa;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpD5E,OAAA;kBAAKwE,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCvB,IAAI,CAACC,GAAG,CAACiD,IAAI,CAAChD,eAAe,CAAC,CAACV,OAAO,CAAC,CAAC;gBAAC;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN5E,OAAA;gBAAKwE,SAAS,EAAC,0BAA0B;gBAAAD,QAAA,gBACvCvE,OAAA;kBAAKwE,SAAS,EAAC,2BAA2B;kBAAAD,QAAA,EAAE2B,IAAI,CAACE;gBAAQ;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChE5E,OAAA;kBAAKwE,SAAS,EAAC,sBAAsB;kBAAAD,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9C5E,OAAA;kBAAKwE,SAAS,EAAC,2BAA2B;kBAAAD,QAAA,EAAE2B,IAAI,CAACG;gBAAQ;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA,GAXEG,KAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAYV,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGD5E,OAAA;QAAKwE,SAAS,EAAC,kBAAkB;QAAAD,QAAA,eAC/BvE,OAAA;UAAAuE,QAAA,GAAG,WACQ,EAAC9D,YAAY,CAAC6F,UAAU,EAAC,aAAW,EAAC7F,YAAY,CAAC8F,iBAAiB,EAAC,WAC7E,EAAC9F,YAAY,CAAC+F,eAAe,CAACC,aAAa,CAACR,MAAM,GAAGxF,YAAY,CAAC+F,eAAe,CAACE,aAAa,CAACT,MAAM,GAAG,CAAC,iBACxGjG,OAAA;YAAAuE,QAAA,GAAM,YAAU,EAAC9D,YAAY,CAAC+F,eAAe,CAACC,aAAa,CAACR,MAAM,GAAGxF,YAAY,CAAC+F,eAAe,CAACE,aAAa,CAACT,MAAM,EAAC,WAAS;UAAA;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACvI;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAEnB,CAAC;AAACpE,EAAA,CAlQIJ,qBAAqB;AAAAuG,EAAA,GAArBvG,qBAAqB;AAoQ3B,eAAeA,qBAAqB;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}