[{"/Users/<USER>/Desktop/Coding/tableyze/frontend/src/index.js": "1", "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/reportWebVitals.js": "2", "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/App.js": "3", "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/pages/AnalysisPage.js": "4", "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/pages/StartPage.js": "5", "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/components/AnalysisCard.js": "6", "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/services/api.js": "7", "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/components/DataUploadContainer.js": "8", "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/components/ContainerBox.js": "9", "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/components/DataAnalysisContainer.js": "10"}, {"size": 486, "mtime": 1756752975271, "results": "11", "hashOfConfig": "12"}, {"size": 362, "mtime": 1756751016190, "results": "13", "hashOfConfig": "12"}, {"size": 505, "mtime": 1756752958427, "results": "14", "hashOfConfig": "12"}, {"size": 7375, "mtime": 1758044200592, "results": "15", "hashOfConfig": "12"}, {"size": 3648, "mtime": 1756754255384, "results": "16", "hashOfConfig": "12"}, {"size": 2324, "mtime": 1756752646713, "results": "17", "hashOfConfig": "12"}, {"size": 1677, "mtime": 1758044065640, "results": "18", "hashOfConfig": "12"}, {"size": 7200, "mtime": 1758051724873, "results": "19", "hashOfConfig": "12"}, {"size": 1722, "mtime": 1758051037509, "results": "20", "hashOfConfig": "12"}, {"size": 9559, "mtime": 1758488019971, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1tntbk5", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/index.js", [], [], "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/reportWebVitals.js", [], [], "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/App.js", [], [], "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/pages/AnalysisPage.js", ["52"], [], "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/pages/StartPage.js", [], [], "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/components/AnalysisCard.js", [], [], "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/services/api.js", [], [], "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/components/DataUploadContainer.js", ["53"], [], "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/components/ContainerBox.js", [], [], "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/components/DataAnalysisContainer.js", ["54"], [], {"ruleId": "55", "severity": 1, "message": "56", "line": 17, "column": 10, "nodeType": "57", "messageId": "58", "endLine": 17, "endColumn": 25}, {"ruleId": "59", "severity": 1, "message": "60", "line": 123, "column": 6, "nodeType": "61", "endLine": 123, "endColumn": 108, "suggestions": "62"}, {"ruleId": "59", "severity": 1, "message": "63", "line": 37, "column": 6, "nodeType": "61", "endLine": 37, "endColumn": 49, "suggestions": "64"}, "no-unused-vars", "'analysisResults' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useCallback has unnecessary dependencies: 'columnsToDrop' and 'preview.columns'. Either exclude them or remove the dependency array.", "ArrayExpression", ["65"], "React Hook useEffect has a missing dependency: 'performAnalysis'. Either include it or remove the dependency array.", ["66"], {"desc": "67", "fix": "68"}, {"desc": "69", "fix": "70"}, "Update the dependencies array to be: [analysis?.id, isNewAnalysis, ensureAnalysisExists, onDatasetCreated]", {"range": "71", "text": "72"}, "Update the dependencies array to be: [visible, analysis?.datasets, analysisData, performAnalysis]", {"range": "73", "text": "74"}, [3923, 4025], "[analysis?.id, isNewAnalysis, ensureAnalysisExists, onDatasetCreated]", [831, 874], "[visible, analysis?.datasets, analysisData, performAnalysis]"]